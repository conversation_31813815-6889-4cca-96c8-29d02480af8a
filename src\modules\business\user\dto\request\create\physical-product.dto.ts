import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsObject, IsOptional,
  ValidateNested,
} from 'class-validator';
import { BusinessShipmentConfigDto, CreateClassificationDto } from '@modules/business/user/dto';
import { BaseProductDto } from '../base-product/base-product.dto';
import { ProductInventoryDto } from '../../product-inventory.dto';
import { ProductPriceDto } from '../product-price/product-price.type';

/**
 * DTO cho việc tạo sản phẩm vật lý (PHYSICAL)
 * <PERSON>ế thừa từ BaseProductDto và thêm các trường đặc thù cho sản phẩm vật lý
 */
export class PhysicalProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Danh sách thông tin tồn kho sản phẩm ở các kho khác nhau - Bắt buộc đối với sản phẩm vật lý. Có thể truyền inventoryId để sử dụng inventory có sẵn hoặc truyền đầy đủ thông tin để tạo mới.',
    type: [ProductInventoryDto],
    isArray: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductInventoryDto)
  inventory: ProductInventoryDto[];

  // Override price để bắt buộc cho PHYSICAL
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Bắt buộc đối với sản phẩm vật lý',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
  })
  @IsNotEmpty()
  @IsObject()
  declare price: ProductPriceDto; // Required for physical products

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => BusinessShipmentConfigDto)
  shipmentConfig?: BusinessShipmentConfigDto;

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [CreateClassificationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];
}