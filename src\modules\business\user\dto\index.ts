// Export main business DTOs
export * from './query-product.dto';
export * from './price.dto';

// Export update DTO
export { BusinessUpdateProductDto, ImageOperationDto } from './update-product.dto';

// Export new DTOs structure - Base and Create DTOs
export * from './base';
export * from './advanced-info';

// Export individual create DTOs
export * from './request/create/physical-product.dto';
export * from './request/create/service-product-create.dto';
export * from './request/create/combo-product-create.dto';

// Export request DTOs
export * from './request/create-products.dto';
export * from './request/batch-create-products.dto';

// Export digital classification DTO
export * from './digital-classification.dto';

// Export old DTOs for backward compatibility (DEPRECATED)
export { BusinessCreateProductDto, BusinessBatchCreateProductDto, BusinessShipmentConfigDto, ProductInventoryDto } from './create-product.dto';

// Export specific items from response to avoid conflicts
export {
  ProductResponseDto as BusinessProductResponseDto,
  BatchProductResponseDto,
  BusinessPresignedUrlImageDto,
  BusinessUploadUrlsDto
} from './product-response.dto';

// Export response DTOs with different names to avoid conflicts
export { ProductResponseDto as TypeSafeProductResponseDto } from './response';

export * from './classification.dto';
export * from './custom-field-metadata.dto';
export * from './bulk-delete-product.dto';
export * from './bulk-delete-product-response.dto';
export * from './bulk-delete-custom-field.dto';
export * from './bulk-delete-custom-field-response.dto';
export * from './product-inventory.dto';
export * from './warehouse-list.dto';
export * from './classification.dto';
export * from './custom-field-metadata.dto';
export * from './bulk-delete-product.dto';
export * from './bulk-delete-product-response.dto';
export * from './bulk-delete-custom-field.dto';
export * from './bulk-delete-custom-field-response.dto';
export * from './product-inventory.dto';
export * from './warehouse-list.dto';




export * from './create-custom-field.dto';

// Tránh xung đột tên với product-response.dto
import {
  CustomFieldResponseDto,
} from './custom-field-response.dto';
export {
  CustomFieldResponseDto,
};

export * from './query-custom-field.dto';
export * from './custom-field-list-response.dto';
export * from './custom-field-detail-response.dto';
export * from './update-custom-field.dto';
export * from './component-response.dto';
export * from './create-custom-field-swagger.dto';
export * from './update-custom-field-swagger.dto';

// User Convert
export * from './user-convert-response.dto';
export * from './query-user-convert.dto';

// User Convert Customer
export * from './create-user-convert-customer.dto';
export * from './create-bulk-user-convert-customer.dto';
export * from './bulk-user-convert-customer-response.dto';
export * from './simple-custom-field.dto';
export * from './update-user-convert-customer.dto';
export * from './merge-user-convert-customer.dto';
export * from './user-convert-customer-response.dto';
export * from './query-user-convert-customer.dto';
export * from './bulk-delete-user-convert-customer.dto';
export * from './bulk-delete-user-convert-customer-response.dto';

// Individual update DTOs
export * from './update-customer-basic-info.dto';
export * from './update-customer-custom-fields.dto';
export * from './update-customer-social-links.dto';

export * from './metadata-field.dto';

// Customer Social
export * from './customer-facebook.dto';
export * from './customer-web.dto';
export * from './update-customer-social.dto';
export * from './update-social-links.dto';

// Report
export * from './report';

// User Order
export * from './user-order-response.dto';
export * from './query-user-order.dto';
export * from './user-order-status.dto';
export * from './create-user-order.dto';
export * from './tracking-response.dto';
export * from './print-order-response.dto';
export * from './print-order-query.dto';
export * from './bulk-delete-user-order.dto';
export * from './bulk-delete-user-order-response.dto';

// User Address
export * from './user-address.dto';
export * from './user-shop-info.dto';

// GHTK
export * from './ghtk';

// GHN
export * from './ghn';

// Shipping Management
export * from './track-order.dto';
export * from './cancel-order.dto';
export * from './return-order.dto';
export * from './print-order.dto';
export * from './calculate-shipping-fee.dto';