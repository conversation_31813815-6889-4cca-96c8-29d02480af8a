import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { BusinessUpdateProductDto } from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm combo (COMBO)
 * Xử lý combo items, combo pricing, combo metadata
 */
@Injectable()
export class ComboProductUpdateProcessor {
  private readonly logger = new Logger(ComboProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
  ) {}

  /**
   * Cập nhật sản phẩm combo hoàn chỉnh
   */
  @Transactional()
  async updateComboProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    _userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Updating COMBO product: ${product.name} (ID: ${product.id}) for user: ${_userId}`,
    );

    // BƯỚC 1: Validate dữ liệu đầu vào cho combo
    this.validateComboProductData(updateDto);

    // BƯỚC 2: Cập nhật combo-specific fields
    this.updateComboFields(product, updateDto);

    // BƯỚC 3: Xử lý combo metadata
    this.updateComboMetadata(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho combo
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý combo items trong advanced info
    await this.processComboItemsUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Combo products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho combo
   */
  private validateComboProductData(updateDto: BusinessUpdateProductDto): void {
    const comboAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    // Validate purchase count nếu có
    if (
      comboAdvancedInfo?.purchaseCount !== undefined &&
      (comboAdvancedInfo.purchaseCount as number) < 0
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }

    // Validate combo items nếu có trong advanced info
    if (comboAdvancedInfo?.comboItems) {
      this.validateComboItems(comboAdvancedInfo.comboItems as unknown[]);
    }

    // Validate combo metadata
    this.validateComboMetadata(updateDto);
  }

  /**
   * Validate combo items
   */
  private validateComboItems(comboItems: unknown[]): void {
    if (!Array.isArray(comboItems) || comboItems.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Combo phải có ít nhất một sản phẩm',
      );
    }

    for (const comboItem of comboItems) {
      const item = comboItem as Record<string, unknown>;

      // Validate required fields
      if (!item.productId && !item.productName) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Combo item phải có productId hoặc productName',
        );
      }

      // Validate quantity
      if (!item.quantity || (item.quantity as number) <= 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng sản phẩm trong combo phải lớn hơn 0',
        );
      }

      // Validate pricing
      if (
        item.originalPrice !== undefined &&
        (item.originalPrice as number) < 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Giá gốc không thể âm',
        );
      }

      if (
        item.discountedPrice !== undefined &&
        (item.discountedPrice as number) < 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Giá giảm không thể âm',
        );
      }

      // Validate discount logic
      if (
        item.originalPrice &&
        item.discountedPrice &&
        (item.discountedPrice as number) > (item.originalPrice as number)
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Giá giảm của "${item.productName as string}" không thể lớn hơn giá gốc`,
        );
      }
    }
  }

  /**
   * Validate combo metadata
   */
  private validateComboMetadata(updateDto: BusinessUpdateProductDto): void {
    const comboAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (comboAdvancedInfo) {
      // Validate combo type
      if (
        comboAdvancedInfo.comboType &&
        typeof comboAdvancedInfo.comboType !== 'string'
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Combo type phải là chuỗi ký tự',
        );
      }

      // Validate combo discount percentage
      if (comboAdvancedInfo.discountPercentage !== undefined) {
        if (
          (comboAdvancedInfo.discountPercentage as number) < 0 ||
          (comboAdvancedInfo.discountPercentage as number) > 100
        ) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Phần trăm giảm giá phải từ 0 đến 100',
          );
        }
      }

      // Validate combo validity period
      if (comboAdvancedInfo.validFrom && comboAdvancedInfo.validTo) {
        const validFrom = new Date(
          comboAdvancedInfo.validFrom as string | number | Date,
        );
        const validTo = new Date(
          comboAdvancedInfo.validTo as string | number | Date,
        );

        if (validFrom >= validTo) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Ngày bắt đầu phải trước ngày kết thúc',
          );
        }
      }
    }
  }

  /**
   * Cập nhật các trường đặc thù cho combo
   */
  private updateComboFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const comboAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    // Cập nhật purchase count
    if (comboAdvancedInfo?.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount =
        comboAdvancedInfo.purchaseCount as number;
      this.logger.log(
        `Updated purchase count to: ${comboAdvancedInfo.purchaseCount as number}`,
      );
    }
  }

  /**
   * Cập nhật combo metadata
   */
  private updateComboMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const comboAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (comboAdvancedInfo) {
      product.metadata = product.metadata || {};

      // Cập nhật combo type
      if (comboAdvancedInfo.comboType !== undefined) {
        product.metadata.comboType = comboAdvancedInfo.comboType as string;
        this.logger.log(
          `Updated combo type to: ${comboAdvancedInfo.comboType as string}`,
        );
      }

      // Cập nhật discount percentage
      if (comboAdvancedInfo.discountPercentage !== undefined) {
        product.metadata.discountPercentage =
          comboAdvancedInfo.discountPercentage as number;
        this.logger.log(
          `Updated discount percentage to: ${comboAdvancedInfo.discountPercentage as number}%`,
        );
      }

      // Cập nhật validity period
      if (comboAdvancedInfo.validFrom !== undefined) {
        product.metadata.validFrom = comboAdvancedInfo.validFrom as string;
        this.logger.log(
          `Updated valid from to: ${comboAdvancedInfo.validFrom as string}`,
        );
      }

      if (comboAdvancedInfo.validTo !== undefined) {
        product.metadata.validTo = comboAdvancedInfo.validTo as string;
        this.logger.log(
          `Updated valid to to: ${comboAdvancedInfo.validTo as string}`,
        );
      }

      // Cập nhật combo description
      if (comboAdvancedInfo.comboDescription !== undefined) {
        product.metadata.comboDescription =
          comboAdvancedInfo.comboDescription as string;
        this.logger.log(`Updated combo description`);
      }
    }
  }

  /**
   * Đảm bảo shipment config = 0 cho combo
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for COMBO product');
  }

  /**
   * Xử lý cập nhật combo items trong advanced info
   */
  private async processComboItemsUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    const comboAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (!comboAdvancedInfo?.comboItems || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(`Updating combo items for COMBO product ${product.id}`);

      // Tìm advanced info hiện tại
      const existingAdvancedInfo =
        await this.productAdvancedInfoRepository.findOne({
          where: { id: product.detail_id },
        });

      if (existingAdvancedInfo) {
        // Xử lý combo items
        const processedComboItems = (
          comboAdvancedInfo.comboItems as unknown[]
        ).map((comboItem: unknown) => {
          const item = comboItem as Record<string, unknown>;
          // Loại bỏ imagesMediaTypes nếu có
          const { imagesMediaTypes, ...itemWithoutImages } = item;
          // Sử dụng imagesMediaTypes để tránh lỗi unused variable
          void imagesMediaTypes;
          return itemWithoutImages;
        });

        // Cập nhật combo trong advanced info (sử dụng field combo có sẵn)
        existingAdvancedInfo.combo = processedComboItems as never;
        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(
          `Successfully updated combo items for COMBO product ${product.id}`,
        );
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Error updating combo items for COMBO product ${product.id}: ${errorMessage}`,
        errorStack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật combo items: ${errorMessage}`,
      );
    }
  }
}
