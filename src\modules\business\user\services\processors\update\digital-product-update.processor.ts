import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  CustomFieldRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { BusinessUpdateProductDto } from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { MetadataHelper } from '../../../helpers/metadata.helper';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm số (DIGITAL)
 * Xử lý delivery method, output type, classifications, variants
 */
@Injectable()
export class DigitalProductUpdateProcessor {
  private readonly logger = new Logger(DigitalProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly metadataHelper: MetadataHelper,
  ) {}

  /**
   * Cập nhật sản phẩm số hoàn chỉnh
   */
  @Transactional()
  async updateDigitalProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    _userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Updating DIGITAL product: ${product.name} (ID: ${product.id}) for user: ${_userId}`,
    );

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm số
    this.validateDigitalProductData(updateDto);

    // BƯỚC 2: Cập nhật digital-specific fields
    this.updateDigitalFields(product, updateDto);

    // BƯỚC 3: Xử lý variant metadata
    await this.processVariantMetadata(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho sản phẩm số
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý advanced info nếu có
    await this.processAdvancedInfoUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Digital products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm số
   */
  private validateDigitalProductData(
    updateDto: BusinessUpdateProductDto,
  ): void {
    const digitalAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (!digitalAdvancedInfo) {
      return;
    }

    // Validate delivery method nếu có
    if (digitalAdvancedInfo.deliveryMethod) {
      const validDeliveryMethods = [
        'DASHBOARD',
        'EMAIL',
        'SMS',
        'DIRECT_MESSAGE',
        'ZALO',
        'AUTO_ACTIVE',
      ];
      if (
        !validDeliveryMethods.includes(
          digitalAdvancedInfo.deliveryMethod as string,
        )
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Delivery method không hợp lệ: ${digitalAdvancedInfo.deliveryMethod as string}`,
        );
      }
    }

    // Validate delivery timing nếu có
    if (digitalAdvancedInfo.deliveryTiming) {
      const validDeliveryTimings = ['IMMEDIATE', 'DELAYED'];
      if (
        !validDeliveryTimings.includes(
          digitalAdvancedInfo.deliveryTiming as string,
        )
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Delivery timing không hợp lệ: ${digitalAdvancedInfo.deliveryTiming as string}`,
        );
      }
    }

    // Validate output type nếu có
    if (digitalAdvancedInfo.outputType) {
      const validOutputTypes = [
        'DOWNLOAD_LINK',
        'ACCESS_CODE',
        'ACCOUNT_INFO',
        'CONTENT',
      ];
      if (
        !validOutputTypes.includes(digitalAdvancedInfo.outputType as string)
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Output type không hợp lệ: ${digitalAdvancedInfo.outputType as string}`,
        );
      }
    }

    // Validate purchase count nếu có
    if (
      digitalAdvancedInfo.purchaseCount !== undefined &&
      (digitalAdvancedInfo.purchaseCount as number) < 0
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }
  }

  /**
   * Cập nhật các trường đặc thù cho sản phẩm số
   */
  private updateDigitalFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const digitalAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (!digitalAdvancedInfo) {
      return;
    }

    // Cập nhật delivery method
    if (digitalAdvancedInfo.deliveryMethod !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.deliveryMethod =
        digitalAdvancedInfo.deliveryMethod as string;
      this.logger.log(
        `Updated delivery method to: ${digitalAdvancedInfo.deliveryMethod as string}`,
      );
    }

    // Cập nhật delivery timing
    if (digitalAdvancedInfo.deliveryTiming !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.deliveryTiming =
        digitalAdvancedInfo.deliveryTiming as string;
      this.logger.log(
        `Updated delivery timing to: ${digitalAdvancedInfo.deliveryTiming as string}`,
      );
    }

    // Cập nhật output type
    if (digitalAdvancedInfo.outputType !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.outputType = digitalAdvancedInfo.outputType as string;
      this.logger.log(
        `Updated output type to: ${digitalAdvancedInfo.outputType as string}`,
      );
    }

    // Cập nhật purchase count
    if (digitalAdvancedInfo.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount =
        digitalAdvancedInfo.purchaseCount as number;
      this.logger.log(
        `Updated purchase count to: ${digitalAdvancedInfo.purchaseCount as number}`,
      );
    }
  }

  /**
   * Xử lý variant metadata cho sản phẩm số
   */
  private async processVariantMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    const digitalAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (digitalAdvancedInfo?.variantMetadata) {
      const variantMetadata = digitalAdvancedInfo.variantMetadata as Record<
        string,
        unknown
      >;
      if (variantMetadata?.variants) {
        const variants = variantMetadata.variants as unknown[];
        this.logger.log(
          `Processing variant metadata with ${variants.length} variants`,
        );

        // Xử lý từng variant
        const processedVariants = await Promise.all(
          variants.map(async (variant: unknown) => {
            const variantObj = variant as Record<string, unknown>;
            // Loại bỏ imagesMediaTypes khỏi variant để tránh lưu vào metadata
            const { imagesMediaTypes, ...variantWithoutImages } = variantObj;
            // Sử dụng imagesMediaTypes để tránh lỗi unused variable
            void imagesMediaTypes;

            // Xử lý custom fields cho variant nếu có
            if (
              variantObj.customFields &&
              Array.isArray(variantObj.customFields) &&
              variantObj.customFields.length > 0
            ) {
              const customFieldIds = this.metadataHelper.extractCustomFieldIds(
                variantObj.customFields as never[],
              );
              const customFields =
                await this.customFieldRepository.findByIds(customFieldIds);
              this.metadataHelper.validateCustomFieldInputs(
                variantObj.customFields as never[],
                customFields,
              );
            }

            return variantWithoutImages;
          }),
        );

        // Cập nhật metadata với variants đã xử lý
        product.metadata = product.metadata || {};
        product.metadata.variants = processedVariants;
      }
    }
  }

  /**
   * Đảm bảo shipment config = 0 cho sản phẩm số
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for DIGITAL product');
  }

  /**
   * Xử lý cập nhật advanced info cho sản phẩm số
   */
  private async processAdvancedInfoUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (!updateDto.advancedInfo || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(
        `Updating advanced info for DIGITAL product ${product.id}`,
      );

      // Tìm advanced info hiện tại
      const existingAdvancedInfo =
        await this.productAdvancedInfoRepository.findOne({
          where: { id: product.detail_id },
        });

      if (existingAdvancedInfo) {
        const digitalAdvancedInfo = updateDto.advancedInfo as unknown as Record<
          string,
          unknown
        >;

        // Cập nhật digital fulfillment flow nếu có
        if (
          digitalAdvancedInfo.deliveryMethod ||
          digitalAdvancedInfo.deliveryTiming ||
          digitalAdvancedInfo.outputType
        ) {
          const fulfillmentFlow = {
            deliveryMethod:
              (digitalAdvancedInfo.deliveryMethod as string) ||
              existingAdvancedInfo.digitalFulfillmentFlow?.deliveryMethod,
            deliveryTiming:
              (digitalAdvancedInfo.deliveryTiming as string) ||
              existingAdvancedInfo.digitalFulfillmentFlow?.deliveryTiming,
            outputType: ((digitalAdvancedInfo.outputType as string) ||
              existingAdvancedInfo.digitalFulfillmentFlow?.outputType) as never,
          } as never;
          existingAdvancedInfo.digitalFulfillmentFlow = fulfillmentFlow;
        }

        // Cập nhật digital output nếu có
        if (digitalAdvancedInfo.variantMetadata) {
          existingAdvancedInfo.digitalOutput =
            digitalAdvancedInfo.variantMetadata as never;
        }

        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(
          `Successfully updated advanced info for DIGITAL product ${product.id}`,
        );
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Error updating advanced info for DIGITAL product ${product.id}: ${errorMessage}`,
        errorStack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật advanced info: ${errorMessage}`,
      );
    }
  }

  /**
   * Validate classifications cho sản phẩm số
   */
  validateDigitalClassifications(classifications: unknown[]): void {
    for (const classification of classifications) {
      const classificationObj = classification as Record<string, unknown>;

      // Validate price cho classification
      if (!classificationObj.price) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Classification phải có giá',
        );
      }

      const price = classificationObj.price as Record<string, unknown>;
      if (!price.listPrice) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Classification phải có giá',
        );
      }

      // Validate quantity constraints
      if (
        classificationObj.minQuantityPerPurchase !== undefined &&
        (classificationObj.minQuantityPerPurchase as number) < 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tối thiểu không thể âm',
        );
      }

      if (
        classificationObj.maxQuantityPerPurchase !== undefined &&
        (classificationObj.maxQuantityPerPurchase as number) < 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tối đa không thể âm',
        );
      }

      if (
        classificationObj.minQuantityPerPurchase &&
        classificationObj.maxQuantityPerPurchase &&
        (classificationObj.minQuantityPerPurchase as number) >
          (classificationObj.maxQuantityPerPurchase as number)
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tối thiểu không thể lớn hơn số lượng tối đa',
        );
      }
    }
  }
}
